package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.embeded.Delimiter;
import com.tripudiotech.migration.entity.embeded.LengthValidation;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.service.EntityService;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AttributeColumnHandlerTest {

    @Mock
    private EntityService entityService;

    @InjectMocks
    private AttributeColumnHandler attributeColumnHandler;

    private FileImport fileImport;
    private EntitySchema entitySchema;
    private RowExtractedData rowExtractedData;
    private ColumnDefinition columnDefinition;
    private ParseSetting parseSetting;

    @BeforeEach
    void setUp() {
        fileImport = FileImport.builder()
                .tenantId("test-tenant")
                .requestedByEmail("<EMAIL>")
                .build();

        entitySchema = new EntitySchema();
        rowExtractedData = RowExtractedData.defaultEmptyResult(1);

        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .build();

        parseSetting = ParseSetting.builder()
                .lengthValidation(LengthValidation.TRUNCATE)
                .multiListDelimiter(Delimiter.COMMA)
                .build();
    }

    @Test
    void shouldHandleBlankColumnValue() {
        // Given
        String columnValue = "";

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAttributeData()).isEmpty();
    }

    @Test
    void shouldHandleStringArrayType() {
        // Given
        String columnValue = "value1,value2,value3";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testArray")
                .targetValueType("STRING_ARRAY")
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        String[] expectedValues = {"value1", "value2", "value3"};
        assertThat(rowExtractedData.getAttributeData().get("testArray")).isEqualTo(expectedValues);
    }

    @Test
    void shouldApplyLengthValidationTruncateMax() {
        // Given
        String columnValue = "verylongvalue";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .targetMaxLength(5)
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAttributeData().get("testAttribute")).isEqualTo("veryl");
    }

    @Test
    void shouldApplyLengthValidationPadMin() {
        // Given
        String columnValue = "abc";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .targetMinLength(5)
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAttributeData().get("testAttribute")).isEqualTo("  abc");
    }

    @Test
    void shouldApplyPatternTransformation() {
        // Given
        String columnValue = "test123value";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("testAttribute")
                .targetValueType("STRING")
                .targetPattern("\\d+")
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAttributeData().get("testAttribute")).isEqualTo("testvalue");
    }

    @Test
    void shouldHandleDataTypeConversionSuccess() {
        // Given
        String columnValue = "TestEntity";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("EntityNameColumn")
                .targetValueType("STRING")
                .targetDataType("EntityType")
                .target("testAttribute.name")
                .build();



        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-123")));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAttributeData().get("EntityNameColumn")).isEqualTo("entity-id-123");
    }

    @Test
    void shouldFailWhenEntityNotFoundForDataTypeConversion() {
        // Given
        String columnValue = "NonExistentEntity";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("EntityNameColumn")
                .targetValueType("STRING")
                .targetDataType("EntityType")
                .target("testAttribute.name")
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.empty()));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitFailure(Duration.ofSeconds(1))
                .assertFailedWith(FileValidatorException.class);
    }

    @Test
    void shouldSkipDataTypeConversionForIdField() {
        // Given
        String columnValue = "entity-id-123";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("EntityIdColumn")
                .targetValueType("STRING")
                .targetDataType("EntityType")
                .target("testAttribute.id")
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        // Should store the original value without conversion
        assertThat(rowExtractedData.getAttributeData().get("EntityIdColumn")).isEqualTo("entity-id-123");
    }

    @Test
    void shouldHandleNullParseSetting() {
        // Given
        String columnValue = "test";
        parseSetting = null;

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, null, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAttributeData().get("testAttribute")).isEqualTo("test");
    }

    @Test
    void shouldHandleDataTypeConversionWithDefaultNameField() {
        // Given
        String columnValue = "TestEntity";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("CompanyNameColumn")
                .targetValueType("STRING")
                .targetDataType("Company")
                .target("companyAttribute") // No field specified, should default to .name
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("company-id-456")));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAttributeData().get("CompanyNameColumn")).isEqualTo("company-id-456");
    }

    @Test
    void shouldHandleDataTypeConversionWithCustomField() {
        // Given
        String columnValue = "COMP-001";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("CompanyCodeColumn")
                .targetValueType("STRING")
                .targetDataType("Company")
                .target("companyCode.code")
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("company-id-789")));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAttributeData().get("CompanyCodeColumn")).isEqualTo("company-id-789");
    }

    @Test
    void shouldHandleStringArrayDataTypeConversionWithIdField() {
        // Given
        String columnValue = "id-1,id-2,id-3";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("EntityIdsColumn")
                .targetValueType("STRING_ARRAY")
                .targetDataType("Entity")
                .target("entityIds.id")
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        String[] expectedValues = {"id-1", "id-2", "id-3"};
        assertThat(rowExtractedData.getAttributeData().get("EntityIdsColumn")).isEqualTo(expectedValues);
    }

    @Test
    void shouldHandleStringArrayDataTypeConversionWithNameField() {
        // Given
        String columnValue = "Entity One,Entity Two,Entity Three";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("EntityNamesColumn")
                .targetValueType("STRING_ARRAY")
                .targetDataType("Entity")
                .target("entityNames.name")
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("Entity One"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-1")));
        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("Entity Two"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-2")));
        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("Entity Three"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-3")));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        String[] expectedIds = {"entity-id-1", "entity-id-2", "entity-id-3"};
        assertThat(rowExtractedData.getAttributeData().get("EntityNamesColumn")).isEqualTo(expectedIds);
    }

    @Test
    void shouldFailStringArrayDataTypeConversionWhenEntityNotFound() {
        // Given
        String columnValue = "Entity One,NonExistent,Entity Three";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("EntityNamesColumn")
                .targetValueType("STRING_ARRAY")
                .targetDataType("Entity")
                .target("entityNames.name")
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("Entity One"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-1")));
        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("NonExistent"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.empty()));
        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("Entity Three"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-3")));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitFailure(Duration.ofSeconds(1))
                .assertFailedWith(FileValidatorException.class);
    }

    @Test
    void shouldHandleDataTypeConversionWithEmptyStringArray() {
        // Given
        String columnValue = "";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("EmptyArrayColumn")
                .targetValueType("STRING_ARRAY")
                .targetDataType("Entity")
                .target("emptyArray.name")
                .build();

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        // Should not add anything to attribute data for empty string
        assertThat(rowExtractedData.getAttributeData()).isEmpty();
    }

    @Test
    void shouldHandleDataTypeConversionWithSingleArrayElement() {
        // Given
        String columnValue = "Single Entity";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("SingleEntityColumn")
                .targetValueType("STRING_ARRAY")
                .targetDataType("Entity")
                .target("singleEntity.name")
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("Single Entity"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("single-entity-id")));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        String[] expectedIds = {"single-entity-id"};
        assertThat(rowExtractedData.getAttributeData().get("SingleEntityColumn")).isEqualTo(expectedIds);
    }

    @Test
    void shouldHandleDataTypeConversionWithCustomDelimiter() {
        // Given
        String columnValue = "Entity One;Entity Two;Entity Three";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("CustomDelimiterColumn")
                .targetValueType("STRING_ARRAY")
                .targetDataType("Entity")
                .target("customDelimiterEntities.name")
                .build();

        parseSetting = ParseSetting.builder()
                .lengthValidation(LengthValidation.TRUNCATE)
                .multiListDelimiter(Delimiter.SEMICOLON)
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("Entity One"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-1")));
        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("Entity Two"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-2")));
        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("Entity Three"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-3")));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        String[] expectedIds = {"entity-id-1", "entity-id-2", "entity-id-3"};
        assertThat(rowExtractedData.getAttributeData().get("CustomDelimiterColumn")).isEqualTo(expectedIds);
    }

    @Test
    void shouldHandleDataTypeConversionWithWhitespaceInValues() {
        // Given
        String columnValue = "  Entity One  ,  Entity Two  ";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("WhitespaceColumn")
                .targetValueType("STRING_ARRAY")
                .targetDataType("Entity")
                .target("whitespaceEntities.name")
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("Entity One"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-1")));
        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("Entity Two"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("entity-id-2")));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        String[] expectedIds = {"entity-id-1", "entity-id-2"};
        assertThat(rowExtractedData.getAttributeData().get("WhitespaceColumn")).isEqualTo(expectedIds);
    }

    @Test
    void shouldHandleDataTypeConversionWithLengthValidationAndDataType() {
        // Given
        String columnValue = "VeryLongEntityName";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("truncatedEntity")
                .targetValueType("STRING")
                .targetDataType("Entity")
                .target("Entity.name")
                .targetMaxLength(10)
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("VeryLongEn"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("truncated-entity-id")));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAttributeData().get("truncatedEntity")).isEqualTo("truncated-entity-id");
    }

    @Test
    void shouldHandleDataTypeConversionWithPatternAndDataType() {
        // Given
        String columnValue = "Entity123Name";
        columnDefinition = ColumnDefinition.builder()
                .fieldValue("patternEntity")
                .targetValueType("STRING")
                .targetDataType("Entity")
                .target("Entity.name")
                .targetPattern("\\d+")
                .build();

        when(entityService.getUniqueEntityIdByAttributeWithCache(
                anyString(), anyString(), anyString(), anyString(), eq("EntityName"), anyString()))
                .thenReturn(Uni.createFrom().item(Optional.of("pattern-entity-id")));

        // When
        Uni<Void> result = attributeColumnHandler.handle(
                "tenant", "token", fileImport, columnValue,
                columnDefinition, parseSetting, entitySchema, rowExtractedData);

        // Then
        result.subscribe().withSubscriber(UniAssertSubscriber.create())
                .awaitItem(Duration.ofSeconds(1))
                .assertCompleted();

        assertThat(rowExtractedData.getAttributeData().get("patternEntity")).isEqualTo("pattern-entity-id");
    }
}
