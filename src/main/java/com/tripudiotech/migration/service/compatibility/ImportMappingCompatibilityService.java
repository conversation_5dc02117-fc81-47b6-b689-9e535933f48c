package com.tripudiotech.migration.service.compatibility;

import com.tripudiotech.base.event.ObjectType;
import com.tripudiotech.base.util.JsonUtil;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.embeded.DataMapping;
import com.tripudiotech.migration.entity.embeded.EntityTypeMappingConfig;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service for handling backward compatibility with legacy import mapping formats
 */
@ApplicationScoped
@Slf4j
public class ImportMappingCompatibilityService {

    /**
     * Convert a data mapping string to the new format and update the FileImport entity
     *
     * @param fileImport      The FileImport entity to update
     * @param dataMappingJson The data mapping JSON string
     * @return The updated FileImport entity
     */
    public FileImport convertLegacyMapping(FileImport fileImport, String dataMappingJson) {
        if (dataMappingJson == null || dataMappingJson.isEmpty()) {
            return fileImport;
        }

        // Create a new FileImport if null
        if (fileImport == null) {
            fileImport = new FileImport();
        }

        try {
            List<DataMapping> dataMappings = JsonUtil.parseJsonList(
                    fileImport.getTenantId(), dataMappingJson, DataMapping.class);

            if (dataMappings != null && !dataMappings.isEmpty()) {

                boolean isLevelBasedBom = dataMappings.stream()
                        .anyMatch(mapping -> mapping.getType() != null &&
                                             mapping.getType() == ObjectType.LEVEL_BASED_BOM);

                if (isLevelBasedBom) {
                    fileImport.setImportType(ObjectType.LEVEL_BASED_BOM.name());
                }

                boolean hasRelation = dataMappings.stream()
                        .anyMatch(mapping -> mapping.getType() != null &&
                                             mapping.getType() == ObjectType.RELATION) ;

                boolean hasFromExistingEntity = dataMappings.stream()
                        .anyMatch(mapping -> mapping.getType() != null &&
                                             mapping.getType() == ObjectType.FROM_EXISTING_ENTITY);

                if (hasRelation && hasFromExistingEntity) {
                    fileImport.setImportType(ObjectType.RELATION.name());
                }

                Map<String, List<DataMapping>> entityTypeMappings = new HashMap<>();

                String key = fileImport.getEntityType() != null ? fileImport.getEntityType() : "default";
                entityTypeMappings.put(key, dataMappings);

                EntityTypeMappingConfig config = EntityTypeMappingConfig.builder()
                        .entityTypeMappings(entityTypeMappings)
                        .build();

                fileImport.setDataMapping(config);

                return fileImport;
            }
        } catch (Exception e) {
            log.debug("Not a DataMapping list: {}", e.getMessage());
        }

        return fileImport;
    }
}
