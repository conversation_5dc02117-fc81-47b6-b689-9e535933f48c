package com.tripudiotech.migration.service.processor.file.handler.column;

import com.tripudiotech.base.event.embed.ColumnDefinition;
import com.tripudiotech.base.event.embed.RowExtractedData;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.embeded.LengthValidation;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.service.EntityService;
import com.tripudiotech.migration.util.DelimiterUtils;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * <PERSON><PERSON> for processing attribute column definitions.
 * Handles string arrays, length validation, pattern matching, and data type conversions.
 **/
@Slf4j
@ApplicationScoped
public class AttributeColumnHandler implements ColumnHandler {

    public static final String SPLIT_PATTERN = ".";
    private static final String STRING_ARRAY = "STRING_ARRAY";
    private static final char DEFAULT_DELIMITER = ',';

    @Inject
    public EntityService entityService;

    @Override
    public Uni<Void> handle(String tenantId, String bearerToken, FileImport fileImport,
                            String columnValue, ColumnDefinition columnDefinition,
                            ParseSetting parseSetting, EntitySchema entitySchema,
                            RowExtractedData rowExtractedData) {

        if (StringUtils.isBlank(columnValue)) {
            return Uni.createFrom().voidItem();
        }
        log.info("Handling attribute column: {}", columnDefinition.getFieldValue());

        if (STRING_ARRAY.equalsIgnoreCase(columnDefinition.getTargetValueType())) {
            return handleStringArray(tenantId, bearerToken, columnValue, columnDefinition, parseSetting, rowExtractedData,fileImport );
        } else {
            return handleSingleValue(tenantId, bearerToken, columnValue, columnDefinition,
                    parseSetting, rowExtractedData,fileImport );
        }
    }

    private Uni<Void> handleStringArray(String tenantId, String bearerToken, String columnValue, ColumnDefinition columnDefinition,
                                        ParseSetting parseSetting, RowExtractedData rowExtractedData, FileImport fileImport) {
        char delimiter = parseSetting != null && parseSetting.getMultiListDelimiter() != null ?
                parseSetting.getMultiListDelimiter().getValue() : DEFAULT_DELIMITER;

        String[] values = DelimiterUtils.splitByDelimiter(columnValue, delimiter);

        if (columnDefinition.getTargetDataType() != null) {
            log.info("Handling data type conversion for array target mapping: {}", columnDefinition.getTarget());
            return handleDataTypeConversionForArray(tenantId, bearerToken, values, columnDefinition, rowExtractedData, fileImport);
        }

        rowExtractedData.getAttributeData().put(columnDefinition.getFieldValue(), values);

        return Uni.createFrom().voidItem();
    }

    private Uni<Void> handleDataTypeConversionForArray(String tenantId, String bearerToken, String[] values,
                                                       ColumnDefinition columnDefinition, RowExtractedData rowExtractedData, FileImport fileImport) {

        String targetMapping = columnDefinition.getTarget();
        String entityType = columnDefinition.getTargetDataType();


        if (!targetMapping.contains(SPLIT_PATTERN)) {
            targetMapping = targetMapping + ".name";
        }

        if (targetMapping.contains(SPLIT_PATTERN)) {
            String attributeName = targetMapping.split("\\.")[0];
            String fieldName = targetMapping.split("\\.")[1];

            if (fieldName.equalsIgnoreCase(SysRoot.Fields.id)) {
                rowExtractedData.getAttributeData().remove(columnDefinition.getFieldValue());
                rowExtractedData.getAttributeData().put(attributeName, values);

                log.info("Extracted ATTRIBUTE. FileImport: {}, AttributeName: {}, Value: {}",
                        rowExtractedData.getRowNumber(), attributeName, Arrays.toString(values));
                return Uni.createFrom().voidItem();
            }

            return Uni.combine().all().unis(
                    Arrays.stream(values)
                            .map(value -> entityService.getUniqueEntityIdByAttributeWithCache(tenantId, fileImport.getRequestedByEmail(),
                                    entityType, fieldName, value.trim(), value))
                            .toArray(Uni[]::new)
            ).withUni(results -> {

                String[] entityIds = results.stream()
                        .map(result -> (Optional<String>) result)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .toArray(String[]::new);

                log.info("RowNum: {}, Found entity for Attribute name: {}, Field name: {}", rowExtractedData.getRowNumber(), attributeName, fieldName);

                if (entityIds.length == 0) {
                    return Uni.createFrom().failure(new FileValidatorException(
                            String.format("Entity not found for %s.%s = %s",
                                    entityType, fieldName, Arrays.toString(values))));
                }
                if (entityIds.length != values.length) {
                    return Uni.createFrom().failure(new FileValidatorException(
                            String.format("Entity not found for %s.%s = %s",
                                    entityType, fieldName, Arrays.toString(values))));
                }
                rowExtractedData.getAttributeData().remove(columnDefinition.getFieldValue());
                rowExtractedData.getAttributeData().put(attributeName, entityIds);

                return Uni.createFrom().voidItem();
            });
        }
        return Uni.createFrom().voidItem();
    }

    private Uni<Void> handleSingleValue(String tenantId, String bearerToken, String columnValue,
                                        ColumnDefinition columnDefinition, ParseSetting parseSetting,
                                        RowExtractedData rowExtractedData, FileImport fileImport) {

        log.info("Handling single value for attribute column: {}", columnDefinition.getFieldValue());

        // Apply length validations
        String processedValue = applyLengthValidations(columnValue, columnDefinition, parseSetting);

        // Apply pattern transformations
        processedValue = applyPatternTransformations(processedValue, columnDefinition);

        // Store the processed value
        rowExtractedData.getAttributeData().put(columnDefinition.getFieldValue(), processedValue);

        // Handle data type conversions if needed
        if (columnDefinition.getTargetDataType() != null) {
            log.info("Handling data type conversion for target mapping: {}", columnDefinition.getTarget());
            return handleDataTypeConversion(tenantId, bearerToken, processedValue,
                    columnDefinition, rowExtractedData,fileImport );
        }

        return Uni.createFrom().voidItem();
    }

    private String applyLengthValidations(String columnValue, ColumnDefinition columnDefinition,
                                          ParseSetting parseSetting) {
        String result = columnValue;

        // Max length validation
        if (columnDefinition.getTargetMaxLength() != null &&
            result.length() > columnDefinition.getTargetMaxLength()) {
            if (parseSetting != null && LengthValidation.TRUNCATE.equals(parseSetting.getLengthValidation())) {
                result = StringUtils.truncate(result, columnDefinition.getTargetMaxLength());
            }
        }

        // Min length validation
        if (columnDefinition.getTargetMinLength() != null &&
            result.length() < columnDefinition.getTargetMinLength()) {
            if (parseSetting != null && LengthValidation.TRUNCATE.equals(parseSetting.getLengthValidation())) {
                result = StringUtils.leftPad(result, columnDefinition.getTargetMinLength());
            }
        }

        return result;
    }

    private String applyPatternTransformations(String columnValue, ColumnDefinition columnDefinition) {
        if (columnDefinition.getTargetPattern() != null) {
            return columnValue.replaceAll(columnDefinition.getTargetPattern(), "");
        }
        return columnValue;
    }

    private Uni<Void> handleDataTypeConversion(String tenantId, String bearerToken, String columnValue,
                                               ColumnDefinition columnDefinition, RowExtractedData rowExtractedData, FileImport fileImport) {
        String targetMapping = columnDefinition.getTarget();

        String entityType = columnDefinition.getTargetDataType();

        log.info("Handling data type conversion for target mapping: {}", targetMapping);

        if (!targetMapping.contains(SPLIT_PATTERN)) {
            targetMapping = targetMapping + ".name";
        }

        if (targetMapping.contains(SPLIT_PATTERN)) {
            String attributeName = targetMapping.split("\\.")[0];
            String fieldName = targetMapping.split("\\.")[1];

            log.info("RowNum: {}, Finding entity ID for Attribute name: {}, Field name: {}", rowExtractedData.getRowNumber(), attributeName, fieldName);

            if (fieldName.equalsIgnoreCase(SysRoot.Fields.id)) {
                rowExtractedData.getAttributeData().remove(columnDefinition.getFieldValue());
                rowExtractedData.getAttributeData().put(attributeName, columnValue);

                return Uni.createFrom().voidItem();
            }

            return entityService.getUniqueEntityIdByAttributeWithCache(tenantId, fileImport.getRequestedByEmail(),
                            entityType, fieldName, columnValue.trim(), columnValue)
                    .flatMap(optionalCorrespondingId -> {
                        log.info("Found entity for {}.{}, ID: {}", entityType, fieldName, optionalCorrespondingId);

                        if (optionalCorrespondingId.isEmpty()) {
                            return Uni.createFrom().failure(new FileValidatorException(
                                    String.format("Entity not found for %s.%s = %s",
                                            entityType, fieldName, columnValue)));
                        }

                        log.info("Extracted ATTRIBUTE. FileImport: {}, AttributeName: {}, Value: {}",
                                rowExtractedData.getRowNumber(), attributeName, optionalCorrespondingId.get());

                        rowExtractedData.getAttributeData().remove(columnDefinition.getFieldValue());
                        rowExtractedData.getAttributeData().put(attributeName, optionalCorrespondingId.get());

                        return Uni.createFrom().voidItem();
                    });
        }

        return Uni.createFrom().voidItem();
    }
}
