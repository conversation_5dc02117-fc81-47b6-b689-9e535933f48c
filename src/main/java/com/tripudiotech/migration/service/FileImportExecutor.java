/*
 * Copyright (c) 2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.migration.service;

import com.tripudiotech.base.client.SchemaManagerClient;
import com.tripudiotech.base.client.dto.request.notification.To;
import com.tripudiotech.base.cloud.storage.provider.StorageClientProviderFactory;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.service.NotificationService;
import com.tripudiotech.base.service.TokenService;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.migration.dto.ImportType;
import com.tripudiotech.migration.dto.response.ImportResult;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.entity.FileImport.Status;
import com.tripudiotech.migration.exception.FileDataPopulateException;
import com.tripudiotech.migration.service.processor.strategy.ImportProcessingStrategyFactory;
import com.tripudiotech.migration.util.NotificationUtil;
import io.micrometer.core.annotation.Timed;
import io.quarkus.hibernate.reactive.panache.Panache;
import io.quarkus.hibernate.reactive.panache.common.WithSession;
import io.smallrye.mutiny.CompositeException;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.tuples.Tuple2;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.OptimisticLockException;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.rest.client.inject.RestClient;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;


/**
 * @author: long.nguyen
 **/

@ApplicationScoped
@Slf4j
public class FileImportExecutor {

    public static final String DIRECT_HTTP = "DIRECT_HTTP";
    public static final String EVENT_DRIVEN = "EVENT_DRIVEN";
    private static final String LOG_PREFIX = "[ImportStatusConsumer";
    @Inject
    FileImportService fileImportService;

    @Inject
    NotificationService notificationService;

    @Inject
    NotificationUtil notificationUtil;

    @ConfigProperty(name = "application.notification.template.fileImportStatusChanged")
    String fileImportStatusChangedTemplate;

    @Inject
    StorageClientProviderFactory storageProviderFactory;

    @RestClient
    SchemaManagerClient schemaManagerClient;

    @Inject
    TokenService tokenService;

    @ConfigProperty(name = "storage.bucket")
    String bucket;

    @ConfigProperty(name = "application.fileImport.notification.enabled")
    boolean shouldNotify;

    @ConfigProperty(name = "application.fileImport.processing.rowThreshold")
    int rowThreshold;

    @Inject
    ImportProcessingStrategyFactory strategyFactory;


    @Timed(
            value = "fileImport_process",
            description = "Time taken to process import data"
    )
    @WithSession
    public Uni<Void> process() {
        return fileImportService.findFirstPendingFileImportOnExistingServiceInstanceId()
                .flatMap(pendingFile -> {
                    if (Objects.isNull(pendingFile)) {
                        log.info("No pending file import to process");
                        return Uni.createFrom().voidItem();
                    }
                    try {
                        var fileContentInputStream = storageProviderFactory.getDefaultStorageProvider()
                                .getFileContentAsInputStream(
                                        bucket,
                                        pendingFile.getObjectStorageUniqueId()
                                );

                        Uni<String> tokenUni = tokenService.getInternalTokenByEmail(pendingFile.getTenantId(),
                                pendingFile.getRequestedByEmail());

                        return tokenUni
                                .flatMap(token -> {
                                    // For MULTIPLE_DATA_TYPES, fetch schemas for all entity types
                                    if (ImportType.MULTIPLE_DATA_TYPES.name().equals(pendingFile.getImportType())) {
                                        log.info("Multiple data types import, fetching schemas for all entity types. TenantId: {}",
                                                pendingFile.getTenantId());

                                        // Create a final copy of pendingFile for use in the lambda
                                        final FileImport fileImportCopy = pendingFile;

                                        // Get schemas for all entity types in the data mapping
                                        return getSchemasForMultipleTypes(token, fileImportCopy.getTenantId(), fileImportCopy)
                                                .map(schemaMap -> {
                                                    // Store the schema map in the file import for later use
                                                    fileImportCopy.setSchemaMap(schemaMap);

                                                    // Return a tuple with the token and a dummy schema
                                                    // The actual schemas will be used in ImportHandlerFacade
                                                    return Tuple2.of(token, new EntitySchema());
                                                });
                                    } else {
                                        return getSchemaFrom(
                                                token,
                                                pendingFile.getTenantId(),
                                                pendingFile.getEntityType()
                                        ).map(entitySchema ->
                                                Tuple2.of(
                                                        token,
                                                        entitySchema
                                                )
                                        );
                                    }
                                })
                                .flatMap(tuple2 -> {
                                    try {
                                        // Create a final copy of pendingFile for use in the lambda
                                        final FileImport fileImportCopy = pendingFile;

                                        String jwtToken = tuple2.getItem1();
                                        EntitySchema entitySchema = tuple2.getItem2();

                                        if (pendingFile.getEntityType() != null) {
                                            fileImportCopy.getSchemaMap().put(pendingFile.getEntityType(), entitySchema);
                                        }

                                        log.info("Tenant {} Start processing file. FileId: {}", fileImportCopy.getTenantId(), fileImportCopy.getId());

                                        Uni<Void> updateStatusToProcessingUni = updateStatusFileImport(
                                                fileImportCopy,
                                                Status.PROCESSING,
                                                null,
                                                null
                                        );
                                        // Determine the strategy based on row count
                                        boolean isEventDriven = fileImportCopy.getTotalRows() > rowThreshold; // Use the same threshold as in application.yaml
                                        fileImportCopy.setProcessingStrategy(isEventDriven ? EVENT_DRIVEN : DIRECT_HTTP);

                                        // Get the appropriate strategy and process the file
                                        Uni<ImportResult> processFileDataUni = Uni.createFrom().item(strategyFactory.getStrategy(fileImportCopy))
                                                .flatMap(strategy -> strategy.processFile(fileImportCopy.getTenantId(), jwtToken, fileContentInputStream, entitySchema, fileImportCopy));

                                        return updateStatusToProcessingUni
                                                .flatMap(vr -> processFileDataUni)
                                                .flatMap(importResult -> {

                                                            // Strategy is already set when selecting the processor

                                                            if (DIRECT_HTTP.equals(fileImportCopy.getProcessingStrategy())) {
                                                                fileImportCopy.setProcessedRows(importResult.getTotalProcessed());
                                                                fileImportCopy.setSuccessRows(importResult.getSuccessCount());
                                                                fileImportCopy.setFailedRows(importResult.getFailedCount());
                                                                fileImportCopy.setEndTime(importResult.getEndTime());
                                                                fileImportCopy.setStartTime(importResult.getStartTime());
                                                                fileImportCopy.setCompletedAt(importResult.getEndTime());
                                                            } else {
                                                                fileImportCopy.setStartTime(importResult.getStartTime());
                                                                fileImportCopy.setTotalBatches(importResult.getTotalBatches());
                                                            }

                                                            Status newStatus = DIRECT_HTTP.equals(fileImportCopy.getProcessingStrategy()) ?
                                                                    Status.COMPLETED : Status.PROCESSING;

                                                            return updateStatusFileImport(fileImportCopy, newStatus, null, null);
                                                        }
                                                )
                                                .onFailure()
                                                .recoverWithUni(
                                                        throwable -> {
                                                            log.error("Tenant {} Failed to process file. FileId: {}",
                                                                    pendingFile.getTenantId(),
                                                                    pendingFile.getId(), throwable);
                                                            return updateStatusFileImport(
                                                                    pendingFile,
                                                                    Status.ERROR,
                                                                    throwable instanceof CompositeException ?
                                                                            getListErrorMsg((CompositeException) throwable) :
                                                                            throwable.getMessage(),
                                                                    throwable
                                                            );
                                                        });

                                    } catch (Exception e) {
                                        return Uni.createFrom().failure(e);
                                    }

                                });
                    } catch (Exception e) {
                        return Uni.createFrom().failure(e);
                    }
                });
    }

    private String getListErrorMsg(CompositeException compositeException) {
        StringBuilder errorMsgSb = new StringBuilder("Exception happened \n");

        boolean checkedHandledException = false;
        for (int i = 0; i < compositeException.getCauses().size(); i++) {
            Throwable throwable = compositeException.getCauses().get(i);
            errorMsgSb.append(String.format("(%s) ", i + 1));
            if (throwable instanceof FileDataPopulateException) {
                if (!checkedHandledException) {
                    errorMsgSb.append("Please check the error details ").append("\n");
                    checkedHandledException = true;
                }
                continue;
            }
            String errMsg =
                    throwable instanceof ServiceException ?
                            ((ServiceException) throwable).getErrorMsg() :
                            throwable.getMessage();
            errorMsgSb.append(errMsg).append(" \n");
        }
        return errorMsgSb.toString();
    }

    private Uni<EntitySchema> getSchemaFrom(
            @NonNull String token,
            @NonNull String tenantId,
            String entityType
    ) {
        return schemaManagerClient.getEntityTypeSchema(
                tenantId,
                token,
                entityType
        ).map(response -> {
            log.info(
                    "Get schema success. TenantId: {}, EntityType: {}",
                    tenantId,
                    entityType
            );
            return response.readEntity(EntitySchema.class);
        });
    }

    /**
     * Get schemas for multiple entity types
     * This is used for MULTIPLE_DATA_TYPES imports
     */
    private Uni<Map<String, EntitySchema>> getSchemasForMultipleTypes(
            @NonNull String token,
            @NonNull String tenantId,
            @NonNull FileImport fileImport
    ) {
        // Get the entity types from the data mapping
        Set<String> entityTypes = new HashSet<>();
        if (fileImport.getDataMapping() != null && fileImport.getDataMapping().getEntityTypeMappings() != null) {
            entityTypes.addAll(fileImport.getDataMapping().getEntityTypeMappings().keySet());
        }

        // If no entity types found, return an empty map
        if (entityTypes.isEmpty()) {
            log.warn("No entity types found in data mapping for multiple data types import. TenantId: {}, FileImportId: {}",
                    tenantId, fileImport.getId());
            return Uni.createFrom().item(new HashMap<>());
        }

        log.info("Fetching schemas for {} entity types. TenantId: {}, FileImportId: {}",
                entityTypes.size(), tenantId, fileImport.getId());

        // Fetch schemas for all entity types
        List<Uni<Tuple2<String, EntitySchema>>> schemaUnis = new ArrayList<>();
        for (String entityType : entityTypes) {
            Uni<Tuple2<String, EntitySchema>> schemaUni = getSchemaFrom(token, tenantId, entityType)
                    .map(schema -> Tuple2.of(entityType, schema));
            schemaUnis.add(schemaUni);
        }

        // Combine all schema unis
        return Uni.join().all(schemaUnis).andCollectFailures()
                .map(schemas -> {
                    Map<String, EntitySchema> schemaMap = new HashMap<>();
                    for (Tuple2<String, EntitySchema> tuple : schemas) {
                        schemaMap.put(tuple.getItem1(), tuple.getItem2());
                    }
                    return schemaMap;
                });
    }

    private Uni<Void> sendNotification(
            @NonNull FileImport fileImport,
            String fromStatus,
            String toStatus
    ) {
        return notificationUtil.sendFileImportStatusNotification(fileImport, fromStatus, toStatus);
    }

    private Uni<Void> updateStatusFileImport(
            @NonNull FileImport fileImport,
            @NonNull FileImport.Status newStatus,
            String errorMsg,
            Throwable throwable
    ) {
        String oldStatus = fileImport.getStatus().name();

        // Set completedAt timestamp when status is COMPLETED
        if (newStatus == Status.COMPLETED && fileImport.getCompletedAt() == null) {
            fileImport.setCompletedAt(java.time.LocalDateTime.now());
        }

        return Uni.createFrom().deferred(() -> fileImportService.updateStatusFileImport(fileImport, newStatus, errorMsg, throwable))
                .flatMap(updatedFileImport -> {
                    log.info(
                            "Tenant {} FileImport status updated. FileId: {}, FromStatus: {}, To Status : {}, ProcessedRows: {}, SuccessRows: {}, FailedRows: {}",
                            fileImport.getTenantId(),
                            fileImport.getId(),
                            oldStatus,
                            newStatus.name(),
                            updatedFileImport.getProcessedRows(),
                            updatedFileImport.getSuccessRows(),
                            updatedFileImport.getFailedRows()
                    );
                    if (!newStatus.isNotifyRequester()) {
                        return Uni.createFrom().voidItem();
                    }
                    return sendNotification(updatedFileImport, oldStatus, newStatus.name());
                })
                .onFailure(OptimisticLockException.class)
                .retry()
                .withBackOff(Duration.ofMillis(100))
                .atMost(3);
    }
}
