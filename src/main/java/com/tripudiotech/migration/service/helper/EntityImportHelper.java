package com.tripudiotech.migration.service.helper;

import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.base.client.dto.response.CreateEntityResponse;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.model.dynamic.EntitySchema;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.migration.entity.FileImport;
import com.tripudiotech.migration.exception.FileDataPopulateException;
import com.tripudiotech.migration.exception.FileValidatorException;
import com.tripudiotech.migration.exception.handler.FileDataPopulateExceptionHandler;
import com.tripudiotech.migration.service.EntityService;
import com.tripudiotech.base.event.embed.RowExtractedData;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Helper class for handling entity imports.
 */
@ApplicationScoped
@Slf4j
public class EntityImportHelper {

    public static final String CREATE_ENTITY = "CREATE_ENTITY";

    @Inject
    EntityService entityService;

    @Inject
    ConverterService converterService;

    @Inject
    FileDataPopulateExceptionHandler fileDataPopulateExceptionHandler;

    @Inject
    ServiceExceptionHandler serviceExceptionHandler;

    @Inject
    MultipleEntityTypeHelper multipleEntityTypeHelper;

    /**
     * Handles the import of regular entity data
     */
    public Uni<CreateEntityResponse> handleEntityImport(
            String jwtToken,
            FileImport fileImport,
            RowExtractedData rowExtractedData
    ) {
        // If we're using an existing entity, handle it differently
        if (rowExtractedData.isUseExistingEntity()) {
            return handleExistingEntityImport(jwtToken, fileImport, rowExtractedData);
        }

        // Regular entity creation flow
        CreateEntityRequest createEntityRequest = buildRequestBody(rowExtractedData);

        if (createEntityRequest.getAttributes().isEmpty()) {
            return handleEmptyEntityRequest(fileImport, rowExtractedData, createEntityRequest);
        }

        // For multiple data types import, we may have a different entity type for this row
        String entityType = fileImport.getEntityType();
        if (rowExtractedData.getDataType() != null) {
            entityType = rowExtractedData.getDataType();
        }

        // Get the schema for this entity type
        EntitySchema entitySchema = multipleEntityTypeHelper.getSchemaForEntityType(entityType, fileImport);
        if (entitySchema == null) {
            return Uni.createFrom().failure(
                    new FileValidatorException("Schema not found for entity type: " + entityType));
        }

        log.debug("Creating entity. FileId: {}, RowNum: {}, EntityType: {}, Attributes: {}",
                fileImport.getId(), rowExtractedData.getRowNumber(),
                entityType, createEntityRequest.getAttributes().keySet());

        return entityService.createEntity(
                        jwtToken,
                        fileImport.getTenantId(),
                        entityType,
                        createEntityRequest
                ).flatMap(response -> processEntityResponse(response, fileImport, rowExtractedData, createEntityRequest))
                .onFailure(Exception.class)
                .recoverWithUni(throwable -> handleEntityCreationFailure(
                        fileImport, rowExtractedData, throwable, createEntityRequest));
    }

    /**
     * Handles import when using an existing entity
     * <p>
     * This method finds an existing entity based on the FROM_EXISTING_ENTITY mapping
     * and creates relationships to it based on the RELATION mappings.
     *
     * @param jwtToken         Authentication token
     * @param fileImport       File import metadata
     * @param rowExtractedData Row data with existing entity information
     * @return A Uni containing the response with the existing entity ID
     */
    public Uni<CreateEntityResponse> handleExistingEntityImport(
            String jwtToken,
            FileImport fileImport,
            RowExtractedData rowExtractedData
    ) {
        log.info("Using existing entity: {}.{} = {}",
                rowExtractedData.getExistingEntityType(),
                rowExtractedData.getExistingEntityField(),
                rowExtractedData.getExistingEntityValue());

        // For multiple data types import, we may have a different entity type for this row
        String entityType = rowExtractedData.getExistingEntityType();
        if (rowExtractedData.getDataType() != null) {
            // If we have a data type, use it instead of the existing entity type
            entityType = rowExtractedData.getDataType();
        }

        // Find the existing entity
        return entityService.findEntityByField(
                        jwtToken,
                        fileImport.getTenantId(),
                        entityType,
                        rowExtractedData.getExistingEntityField(),
                        rowExtractedData.getExistingEntityValue())
                .flatMap(response -> processExistingEntityResponse(response, jwtToken, fileImport, rowExtractedData))
                .onFailure(Exception.class)
                .recoverWithUni(throwable -> handleEntityLookupFailure(
                        fileImport, rowExtractedData, throwable));
    }

    /**
     * Processes the response from finding an existing entity
     * <p>
     * This method extracts the entity ID from the response and creates relationships
     * to the existing entity based on the RELATION mappings.
     *
     * @param response         Response from finding the entity
     * @param jwtToken         Authentication token
     * @param fileImport       File import metadata
     * @param rowExtractedData Row data with relation information
     * @return A Uni containing the response with the existing entity ID
     */
    private Uni<CreateEntityResponse> processExistingEntityResponse(
            Response response,
            String jwtToken,
            FileImport fileImport,
            RowExtractedData rowExtractedData
    ) {
        response.bufferEntity();

        // Parse the response to get the entity ID
        PageResponse pageResponse =
                response.readEntity(PageResponse.class);

        // Check if we found the entity
        if (pageResponse.getData() == null || pageResponse.getData().isEmpty()) {
            String errorMsg = String.format("Existing entity not found: %s.%s = %s",
                    rowExtractedData.getExistingEntityType(),
                    rowExtractedData.getExistingEntityField(),
                    rowExtractedData.getExistingEntityValue());
            log.error(errorMsg);
            return Uni.createFrom().failure(new FileDataPopulateException(
                    errorMsg,
                    rowExtractedData.getRowNumber(),
                    "FIND_ENTITY",
                    null,
                    null
            ));
        }


        EntityWithPermission entity = converterService.convertMapToValue(
                (Map<String, Object>) pageResponse.getData().get(0),
                EntityWithPermission.class
        );


        log.info("Found existing entity with ID: {}", entity.getId());


        // If there are no relations to create, just return the entity ID
        if (rowExtractedData.getRelationIdByNameMap() == null || rowExtractedData.getRelationIdByNameMap().isEmpty()) {
            log.info("No relations found for existing entity: {}", entity.getId());

            return serviceExceptionHandler.handleServiceException(
                    fileImport,
                    rowExtractedData.getRowNumber(),
                    "CREATE_ENTITY",
                    null,
                    new ServiceException(fileImport.getTenantId(), BusinessErrorCode.RECORD_NOT_FOUND, "No relations found for existing entity")
            );
        }

        // Create relations to the existing entity
        return createRelationsForExistingEntity(jwtToken, fileImport, rowExtractedData, entity);
    }

    /**
     * Creates relations for an existing entity
     * <p>
     * This method creates relationships between the existing entity and other entities
     * defined in the RELATION mappings.
     *
     * @param jwtToken         Authentication token
     * @param fileImport       File import metadata
     * @param rowExtractedData Row data with relation information
     * @param entity           ID of the existing entity
     * @return A Uni containing the response with the existing entity ID
     */
    private Uni<CreateEntityResponse> createRelationsForExistingEntity(
            String jwtToken,
            FileImport fileImport,
            RowExtractedData rowExtractedData,
            EntityWithPermission entity
    ) {
        java.util.List<Uni<Void>> relationUnis = new java.util.ArrayList<>();

        rowExtractedData.getRelationIdByNameMap().forEach((relationName, entityIdForRelation) -> {
            log.info("Creating relation: {} from {} to {}",
                    relationName, entity.getId(), entityIdForRelation);

            relationUnis.add(
                    entityService.createRelation(
                                    jwtToken,
                                    fileImport.getTenantId(),
                                    entity.getId(),
                                    relationName,
                                    entityIdForRelation)
                            .map(response -> null));

        });

        // Wait for all relations to be created
        return Uni.join().all(relationUnis)
                .andCollectFailures()
                .map(results -> {
                    CreateEntityResponse response = new CreateEntityResponse();
                    response.setId(entity.getId());
                    response.setProperties(entity.getProperties());
                    return response;
                });
    }

    /**
     * Handles failures during entity lookup
     */
    private Uni<CreateEntityResponse> handleEntityLookupFailure(
            FileImport fileImport,
            RowExtractedData rowExtractedData,
            Throwable throwable
    ) {
        String errorMsg = String.format("Failed to find existing entity: %s.%s = %s",
                rowExtractedData.getExistingEntityType(),
                rowExtractedData.getExistingEntityField(),
                rowExtractedData.getExistingEntityValue());

        log.error(errorMsg, throwable);

        FileDataPopulateException exception = serviceExceptionHandler.createFileDataPopulateException(
                "FIND_ENTITY",
                rowExtractedData.getRowNumber(),
                throwable,
                null
        );

        return fileDataPopulateExceptionHandler.handle(fileImport, exception)
                .flatMap(vr -> Uni.createFrom().failure(exception));
    }

    /**
     * Handles the case when an entity request has no attributes
     */
    private Uni<CreateEntityResponse> handleEmptyEntityRequest(
            FileImport fileImport,
            RowExtractedData rowExtractedData,
            CreateEntityRequest createEntityRequest
    ) {
        try {
            log.warn("Empty entity request for row {}. FileId: {}",
                    rowExtractedData.getRowNumber(), fileImport.getId());

            FileDataPopulateException fileDataPopulateException = new FileDataPopulateException(
                    String.format(
                            "CREATED_ENTITY Skip processing as empty request. RowNumber: %s",
                            rowExtractedData.getRowNumber()
                    ),
                    rowExtractedData.getRowNumber(),
                    CREATE_ENTITY,
                    converterService.convertValueToJsonString(
                            createEntityRequest, true
                    ),
                    ""
            );
            return fileDataPopulateExceptionHandler.handle(
                            fileImport,
                            fileDataPopulateException
                    )
                    .flatMap(vr -> Uni.createFrom().failure(fileDataPopulateException));
        } catch (Exception ex) {
            // Preserve the original cause if available
            Throwable rootCause = ex.getCause() != null ? ex.getCause() : ex;
            log.error("Error handling empty entity request. FileId: {}, RowNum: {}",
                    fileImport.getId(), rowExtractedData.getRowNumber(), rootCause);
            return Uni.createFrom().failure(rootCause);
        }
    }

    /**
     * Processes the response from entity creation
     */
    private Uni<CreateEntityResponse> processEntityResponse(
            Response response,
            FileImport fileImport,
            RowExtractedData rowExtractedData,
            CreateEntityRequest createEntityRequest
    ) {
        response.bufferEntity();
        if (Response.Status.Family.familyOf(response.getStatus())
                .equals(Response.Status.Family.SUCCESSFUL)) {

            CreateEntityResponse createEntityResponse = response.readEntity(
                    CreateEntityResponse.class);

            log.info("CREATE_ENTITY succeeded with EntityID {}. FileImportId: {}, RowNumber: {}",
                    createEntityResponse.getId(), fileImport.getId(), rowExtractedData.getRowNumber());

            return Uni.createFrom().item(createEntityResponse);
        }

        String responseBody = response.readEntity(String.class);
        FileDataPopulateException exception = new FileDataPopulateException(
                String.format(
                        "CREATED_ENTITY response NOT success status. RowNumber: %s",
                        rowExtractedData.getRowNumber()
                ),
                rowExtractedData.getRowNumber(),
                CREATE_ENTITY,
                converterService.convertValueToJsonString(createEntityRequest, true),
                responseBody
        );

        log.error("Failed to create entity. FileId: {}, RowNum: {}, Response: {}",
                fileImport.getId(), rowExtractedData.getRowNumber(), responseBody);
        return Uni.createFrom().failure(exception);
    }

    /**
     * Handles failures during entity creation
     */
    private Uni<CreateEntityResponse> handleEntityCreationFailure(
            FileImport fileImport,
            RowExtractedData rowExtractedData,
            Throwable throwable,
            CreateEntityRequest createEntityRequest
    ) {
        try {
            // If it's a ServiceException, use our common handler
            if (throwable instanceof ServiceException serviceException) {
                return serviceExceptionHandler.handleServiceException(
                        fileImport,
                        rowExtractedData.getRowNumber(),
                        CREATE_ENTITY,
                        createEntityRequest,
                        serviceException
                );
            }

            // Otherwise, use the existing approach for other types of exceptions
            FileDataPopulateException fileDataPopulateException = serviceExceptionHandler.createFileDataPopulateException(
                    CREATE_ENTITY,
                    rowExtractedData.getRowNumber(),
                    throwable,
                    createEntityRequest
            );
            return fileDataPopulateExceptionHandler.handle(
                            fileImport,
                            fileDataPopulateException
                    )
                    .flatMap(vr -> Uni.createFrom().failure(fileDataPopulateException));
        } catch (Exception e) {
            log.error("Failed to handle exception. FileId: {}, Action: {}, RowNum: {}",
                    fileImport.getId(), "CREATED_ENTITY", rowExtractedData.getRowNumber(), e);
            return Uni.createFrom().failure(throwable);
        }
    }

    /**
     * Builds a CreateEntityRequest from RowExtractedData
     */
    public CreateEntityRequest buildRequestBody(RowExtractedData rowExtractedData) {
        CreateEntityRequest createEntityRequest = new CreateEntityRequest();
        createEntityRequest.setAttributes(rowExtractedData.getAttributeData());

        if (StringUtils.isNoneBlank(rowExtractedData.getLifecycleId())) {
            createEntityRequest.setLifeCycle(CreateEntityRequest.EntityLifeCycle.builder()
                    .id(rowExtractedData.getLifecycleId())
                    .startState(rowExtractedData.getLifeCycleState())
                    .build()
            );
        }

        addRelationsToRequest(rowExtractedData, createEntityRequest);

        return createEntityRequest;
    }

    /**
     * Adds relations to a CreateEntityRequest
     * <p>
     * This method uses the relationMetadataMap if available, falling back to the legacy maps if needed.
     */
    public void addRelationsToRequest(RowExtractedData rowExtractedData, CreateEntityRequest createEntityRequest) {
        // Check if we have any relations to add
        if ((rowExtractedData.getRelationMetadataMap() == null || rowExtractedData.getRelationMetadataMap().isEmpty()) &&
            (rowExtractedData.getRelationIdByNameMap() == null || rowExtractedData.getRelationIdByNameMap().isEmpty())) {
            return;
        }

        Set<CreateEntityRequest.RelationRequest> relationRequests = new HashSet<>();

        // Prefer using the relationMetadataMap if available
        if (rowExtractedData.getRelationMetadataMap() != null && !rowExtractedData.getRelationMetadataMap().isEmpty()) {
            rowExtractedData.getRelationMetadataMap().forEach((relationName, metadata) -> {
                if (StringUtils.isNoneBlank(metadata.getToEntityId()) && StringUtils.isNoneBlank(metadata.getToEntityType())) {
                    relationRequests.add(
                            CreateEntityRequest.RelationRequest.builder()
                                    .entityType(metadata.getToEntityType())
                                    .name(relationName)
                                    .entityId(metadata.getToEntityId())
                                    .build()
                    );
                } else {
                    log.warn(
                            "Ignore the relation in request body due to missing required field. RelationName: {}, EntityType: {}, EntityId: {}",
                            relationName,
                            metadata.getToEntityType(),
                            metadata.getToEntityId()
                    );
                }
            });
        } else {
            // Fall back to legacy maps if needed
            rowExtractedData.getRelationIdByNameMap().forEach((relationName, entityId) -> {
                String entityType = rowExtractedData.getTargetEntityTypeByRelationName().get(relationName);
                if (StringUtils.isNoneBlank(entityId) && StringUtils.isNoneBlank(entityType)) {
                    relationRequests.add(
                            CreateEntityRequest.RelationRequest.builder()
                                    .entityType(entityType)
                                    .name(relationName)
                                    .entityId(entityId)
                                    .build()
                    );
                } else {
                    log.warn(
                            "Ignore the relation in request body due to missing required field. RelationName: {}, EntityType: {}, EntityId: {}",
                            relationName,
                            entityType,
                            entityId
                    );
                }
            });
        }

        if (!relationRequests.isEmpty()) {
            createEntityRequest.setRelations(relationRequests);
        }
    }
}
